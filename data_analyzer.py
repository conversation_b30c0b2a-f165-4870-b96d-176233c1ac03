import pandas as pd
import matplotlib.pyplot as plt
import os
from datetime import datetime
import json

class DataAnalyzer:
    def __init__(self, data_dir="output_data"):
        self.data_dir = data_dir
        self.output_dir = "analysis_results"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置中文字体
        plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    
    def analyze_all_data(self):
        """分析所有数据并生成报告"""
        # 读取所有数据
        user_info_df = self._load_user_info_data()
        stats_df = self._load_stats_data()
        
        if user_info_df is None and stats_df is None:
            print("没有数据可分析")
            return
        
        # 生成分析结果
        analysis_results = {}
        
        if user_info_df is not None:
            # 分析用户信息
            analysis_results["用户信息分析"] = self._analyze_user_info(user_info_df)
        
        if stats_df is not None:
            # 分析统计数据
            analysis_results["统计数据分析"] = self._analyze_stats(stats_df)
        
        # 平台对比分析
        if user_info_df is not None and stats_df is not None:
            analysis_results["平台对比"] = self._compare_platforms(user_info_df, stats_df)
        
        # 保存分析结果
        self._save_analysis_results(analysis_results)
        
        return analysis_results
    
    def _load_user_info_data(self):
        """加载用户信息数据"""
        user_info_files = [f for f in os.listdir(self.data_dir) if f.endswith(".json") and "user_info" in f]
        
        if not user_info_files:
            return None
        
        all_data = []
        
        for file in user_info_files:
            file_path = os.path.join(self.data_dir, file)
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                
                # 提取平台信息
                platform = file.split("_")[0]
                
                # 规范化数据结构
                row = {
                    "平台": platform,
                    "账号ID": data.get("account_id"),
                    "账号名称": data.get("name"),
                    "粉丝数": data.get("follower_count"),
                    "关注数": data.get("following_count"),
                    "作品数": data.get("post_count", data.get("video_count")),
                    "总点赞数": data.get("like_count", data.get("total_likes")),
                    "总播放量": data.get("total_views"),
                    "更新时间": data.get("update_time")
                }
                
                all_data.append(row)
            except Exception as e:
                print(f"加载文件 {file} 失败: {e}")
        
        if not all_data:
            return None
        
        return pd.DataFrame(all_data)
    
    def _load_stats_data(self):
        """加载统计数据"""
        stats_files = [f for f in os.listdir(self.data_dir) if f.endswith(".json") and "stats" in f]
        
        if not stats_files:
            return None
        
        all_data = []
        
        for file in stats_files:
            file_path = os.path.join(self.data_dir, file)
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                
                # 提取平台信息
                platform = file.split("_")[0]
                
                # 规范化数据结构
                row = {
                    "平台": platform,
                    "账号ID": data.get("account_id"),
                    "互动率": data.get("engagement_rate"),
                    "平均点赞数": data.get("avg_likes"),
                    "平均评论数": data.get("avg_comments"),
                    "平均播放量": data.get("avg_views"),
                    "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                all_data.append(row)
            except Exception as e:
                print(f"加载文件 {file} 失败: {e}")
        
        if not all_data:
            return None
        
        return pd.DataFrame(all_data)
    
    def _analyze_user_info(self, df):
        """分析用户信息数据"""
        results = {}
        
        # 按平台统计账号数量
        platform_counts = df["平台"].value_counts()
        results["平台分布"] = platform_counts.to_dict()
        
        # 统计各平台粉丝总数
        follower_sums = df.groupby("平台")["粉丝数"].sum()
        results["各平台粉丝总数"] = follower_sums.to_dict()
        
        # 找出粉丝数最多的账号
        max_follower_account = df.loc[df["粉丝数"].idxmax()]
        results["粉丝数最多的账号"] = max_follower_account.to_dict()
        
        # 创建图表
        # 平台账号分布饼图
        plt.figure(figsize=(8, 6))
        platform_counts.plot(kind="pie", autopct='%1.1f%%', startangle=90)
        plt.title("各平台账号分布")
        plt.axis('equal')
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/平台账号分布.png")
        
        # 各平台粉丝数对比图
        plt.figure(figsize=(10, 6))
        follower_sums.plot(kind="bar")
        plt.title("各平台粉丝总数对比")
        plt.xlabel("平台")
        plt.ylabel("粉丝总数")
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/各平台粉丝总数对比.png")
        
        return results
    
    def _analyze_stats(self, df):
        """分析统计数据"""
        results = {}
        
        # 各平台互动率对比
        engagement_rate_avg = df.groupby("平台")["互动率"].mean().round(2)
        results["各平台平均互动率"] = engagement_rate_avg.to_dict()
        
        # 各平台平均数据对比
        avg_metrics = ["平均点赞数", "平均评论数", "平均播放量"]
        platform_avg = df.groupby("平台")[avg_metrics].mean().round(2)
        results["各平台平均数据"] = platform_avg.to_dict()
        
        # 创建图表
        # 各平台互动率对比图
        plt.figure(figsize=(10, 6))
        engagement_rate_avg.plot(kind="bar")
        plt.title("各平台平均互动率对比")
        plt.xlabel("平台")
        plt.ylabel("互动率 (%)")
        plt.xticks(rotation=0)
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/各平台平均互动率对比.png")
        
        # 各平台平均数据雷达图
        fig = plt.figure(figsize=(8, 8))
        ax = fig.add_subplot(111, polar=True)
        
        platforms = platform_avg.index
        angles = [n / len(avg_metrics) * 2 * 3.14159 for n in range(len(avg_metrics))]
        angles += angles[:1]
        
        for platform in platforms:
            values = platform_avg.loc[platform].tolist()
            values += values[:1]
            ax.plot(angles, values, linewidth=2, label=platform)
            ax.fill(angles, values, alpha=0.1)
        
        plt.title("各平台平均数据对比")
        ax.set_thetagrids([angle * 180 / 3.14159 for angle in angles[:-1]], avg_metrics)
        plt.legend(loc='upper right')
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/各平台平均数据雷达图.png")
        
        return results
    
    def _compare_platforms(self, user_info_df, stats_df):
        """比较不同平台的数据"""
        results = {}
        
        # 合并数据
        merged_df = pd.merge(
            user_info_df[["平台", "账号ID", "粉丝数", "作品数", "总点赞数"]],
            stats_df[["平台", "账号ID", "互动率"]],
            on=["平台", "账号ID"]
        )
        
        # 计算各平台的关键指标
        platform_metrics = merged_df.groupby("平台").agg({
            "粉丝数": "mean",
            "作品数": "mean",
            "总点赞数": "mean",
            "互动率": "mean"
        }).round(2).reset_index()
        
        results["各平台关键指标对比"] = platform_metrics.to_dict()
        
        # 创建综合对比图表
        plt.figure(figsize=(12, 8))
        
        # 粉丝数对比
        plt.subplot(2, 2, 1)
        plt.bar(platform_metrics["平台"], platform_metrics["粉丝数"])
        plt.title("平均粉丝数")
        plt.xticks(rotation=45)
        
        # 作品数对比
        plt.subplot(2, 2, 2)
        plt.bar(platform_metrics["平台"], platform_metrics["作品数"])
        plt.title("平均作品数")
        plt.xticks(rotation=45)
        
        # 总点赞数对比
        plt.subplot(2, 2, 3)
        plt.bar(platform_metrics["平台"], platform_metrics["总点赞数"])
        plt.title("平均总点赞数")
        plt.xticks(rotation=45)
        
        # 互动率对比
        plt.subplot(2, 2, 4)
        plt.bar(platform_metrics["平台"], platform_metrics["互动率"])
        plt.title("平均互动率 (%)")
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/各平台综合对比.png")
        
        return results
    
    def _save_analysis_results(self, results):
        """保存分析结果到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"{self.output_dir}/analysis_results_{timestamp}.json"
        
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        # 导出到Excel
        self._export_to_excel(results, timestamp)
        
        print(f"分析结果已保存到 {output_file}")
    
    def _export_to_excel(self, results, timestamp):
        """将分析结果导出到Excel"""
        output_file = f"{self.output_dir}/analysis_summary_{timestamp}.xlsx"
        writer = pd.ExcelWriter(output_file, engine='openpyxl')
        
        # 将每个分析结果表写入不同的sheet
        for sheet_name, data in results.items():
            if isinstance(data, dict):
                # 处理字典数据
                df = pd.DataFrame.from_dict(data, orient='index')
                df.to_excel(writer, sheet_name=sheet_name)
            elif isinstance(data, pd.DataFrame):
                # 处理DataFrame数据
                data.to_excel(writer, sheet_name=sheet_name)
        
        writer.close()
        print(f"分析摘要已导出到 {output_file}")