import json
import os
import tkinter as tk
from tkinter import ttk, messagebox
import webbrowser
import uuid
import threading
import requests
import time
from urllib.parse import urlparse, parse_qs
import pandas as pd

class AccountManager:
    def __init__(self, config_file="accounts.json"):
        self.config_file = config_file
        self.accounts = self.load_accounts()
        self.platforms = ["小红书", "抖音", "快手"]
        self.cookies = {}
    
    def load_accounts(self):
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, "r", encoding="utf-8") as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载账号配置失败: {e}")
        return []
    
    def save_accounts(self):
        with open(self.config_file, "w", encoding="utf-8") as f:
            json.dump(self.accounts, f, ensure_ascii=False, indent=2)
    
    def add_account(self, name, platform, address):
        account = {
            "id": str(uuid.uuid4()),
            "name": name,
            "platform": platform,
            "address": address,
            "cookie": None,
            "authorized": False,
            "last_updated": None
        }
        self.accounts.append(account)
        self.save_accounts()
        return account
    
    def update_account(self, account_id, **kwargs):
        for account in self.accounts:
            if account["id"] == account_id:
                account.update(kwargs)
                self.save_accounts()
                return True
        return False
    
    def delete_account(self, account_id):
        self.accounts = [a for a in self.accounts if a["id"] != account_id]
        self.save_accounts()
    
    def get_account(self, account_id):
        for account in self.accounts:
            if account["id"] == account_id:
                return account
        return None
    
    def get_platform_accounts(self, platform):
        return [a for a in self.accounts if a["platform"] == platform]
    
    def authorize_account(self, account_id, auth_callback=None):
        account = self.get_account(account_id)
        if not account:
            return False
        
        platform = account["platform"]
        auth_url = self._get_auth_url(platform, account_id)
        
        # 打开浏览器进行授权
        webbrowser.open(auth_url)
        
        # 这里应该实现一个等待授权完成的机制
        # 简化版：使用回调函数通知授权完成
        if auth_callback:
            # 模拟授权完成，实际应通过监听登录回调或用户手动确认
            threading.Thread(target=self._simulate_auth, args=(account_id, auth_callback)).start()
        
        return True
    
    def _get_auth_url(self, platform, account_id):
        # 根据平台生成授权URL
        # 这里使用示例URL，实际应替换为真实的授权URL
        base_urls = {
            "小红书": "https://www.xiaohongshu.com/auth?app_id=12345&redirect_uri=http://localhost/callback",
            "抖音": "https://www.douyin.com/oauth/authorize?client_key=abcdef&redirect_uri=http://localhost/callback",
            "快手": "https://www.kuaishou.com/oauth2/authorize?client_id=123456&redirect_uri=http://localhost/callback"
        }
        
        return f"{base_urls.get(platform, '')}&state={account_id}"
    
    def _simulate_auth(self, account_id, callback):
        # 模拟授权过程，实际应用中应替换为真实的授权逻辑
        print(f"模拟{account_id}的授权过程...")
        time.sleep(5)  # 模拟等待用户登录
        
        # 模拟获取到的cookie
        mock_cookie = {
            "name": f"mock_cookie_{account_id}",
            "value": "session_token=**********; user_id=12345",
            "expires": int(time.time()) + 3600,  # 1小时后过期
            "domain": ".example.com"
        }
        
        # 更新账号信息
        self.update_account(
            account_id,
            cookie=mock_cookie,
            authorized=True,
            last_updated=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
        # 保存cookie
        self.cookies[account_id] = mock_cookie
        
        # 调用回调函数
        if callback:
            callback(account_id, True)
    
    def export_to_excel(self, output_file="accounts.xlsx"):
        df = pd.DataFrame(self.accounts)
        df.to_excel(output_file, index=False)
        return output_file