#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox
import os

def test_gui():
    """测试GUI是否能正常显示"""
    try:
        print("创建根窗口...")
        root = tk.Tk()
        root.title("GUI测试 - 社交媒体数据抓取工具")
        root.geometry("800x500")
        
        # 确保窗口显示在前台
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(lambda: root.attributes('-topmost', False))
        
        print("创建主框架...")
        main_frame = ttk.Frame(root, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="社交媒体数据抓取工具", font=("Arial", 16, "bold"))
        title_label.pack(pady=10)
        
        # 状态信息
        status_label = ttk.Label(main_frame, text="✅ GUI测试成功！界面正常显示", font=("Arial", 12))
        status_label.pack(pady=10)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=20)
        
        # 测试按钮
        def show_message():
            messagebox.showinfo("测试", "按钮点击测试成功！")
        
        test_button = ttk.Button(button_frame, text="测试按钮", command=show_message)
        test_button.pack(side=tk.LEFT, padx=10)
        
        # 退出按钮
        exit_button = ttk.Button(button_frame, text="退出", command=root.quit)
        exit_button.pack(side=tk.LEFT, padx=10)
        
        # 信息文本
        info_text = tk.Text(main_frame, height=10, width=60)
        info_text.pack(pady=20, fill=tk.BOTH, expand=True)
        
        info_content = """
GUI测试信息：

1. 如果您能看到这个窗口，说明Tkinter GUI正常工作
2. 窗口标题：GUI测试 - 社交媒体数据抓取工具
3. 窗口大小：800x500
4. 包含按钮和文本框等基本组件

如果这个测试窗口能正常显示，那么主程序的GUI也应该能正常工作。

可能的问题：
- 主程序初始化时出现异常
- 窗口被其他应用程序遮挡
- 窗口位置超出屏幕范围

解决方案：
1. 检查终端输出的错误信息
2. 使用Cmd+Tab切换到Python应用程序
3. 重新启动程序
        """
        
        info_text.insert(tk.END, info_content)
        info_text.config(state=tk.DISABLED)
        
        print("GUI组件创建完成，开始主循环...")
        root.mainloop()
        
    except Exception as e:
        print(f"GUI测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gui()
