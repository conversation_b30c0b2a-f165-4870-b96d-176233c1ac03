# 社交媒体数据抓取工具

这是一个用于抓取和分析社交媒体数据的GUI工具，支持小红书、抖音、快手等平台。

## 功能特性

- 📱 支持多个社交媒体平台（小红书、抖音、快手）
- 👤 账号管理和授权
- 📊 数据抓取（用户信息、帖子数据、统计信息）
- 📈 数据分析和可视化
- 📋 Excel导出功能
- 🖥️ 友好的图形用户界面

## 系统要求

- Python 3.9+
- macOS/Linux/Windows
- 网络连接

## 安装和启动

### 方法1：使用启动脚本（推荐）

```bash
# 给启动脚本添加执行权限
chmod +x start.sh

# 启动程序
./start.sh
```

### 方法2：手动安装依赖

```bash
# 安装依赖包
pip3 install -r requirements.txt

# 启动程序
python3 main.py
```

## 使用说明

1. **添加账号**：点击"添加账号"按钮，填写配置名称、选择平台、输入地址路径
2. **授权登录**：右键点击账号，选择"登录授权"进行账号授权
3. **抓取数据**：选择已授权的账号，点击"抓取数据"选择要抓取的数据类型
4. **分析数据**：点击"分析数据"对抓取的数据进行分析
5. **导出数据**：点击"导出数据"将数据导出为Excel文件

## 目录结构

```
├── main.py              # 主程序入口
├── account_manager.py   # 账号管理模块
├── data_crawler.py      # 数据抓取模块
├── data_analyzer.py     # 数据分析模块
├── requirements.txt     # 依赖包列表
├── start.sh            # 启动脚本
├── README.md           # 说明文档
├── accounts.json       # 账号配置文件（自动生成）
├── output_data/        # 抓取数据存储目录（自动生成）
└── analysis_results/   # 分析结果存储目录（自动生成）
```

## 注意事项

- 首次启动时matplotlib会构建字体缓存，可能需要等待一段时间
- 请确保网络连接正常
- 抓取数据时请遵守各平台的使用条款和频率限制
- 建议定期备份账号配置和抓取的数据

## 故障排除

### 常见问题

1. **ImportError: No module named 'xxx'**
   - 解决方案：运行 `pip3 install -r requirements.txt` 安装依赖

2. **架构兼容性问题（ARM64 vs x86_64）**
   - 解决方案：重新安装相关包 `pip3 uninstall package_name -y && pip3 install package_name`

3. **Tk弃用警告**
   - 解决方案：设置环境变量 `export TK_SILENCE_DEPRECATION=1`

4. **程序无法启动**
   - 检查Python版本：`python3 --version`
   - 检查依赖安装：`python3 -c "import pandas, numpy, matplotlib"`

## 开发说明

这是一个演示版本，实际的API调用和数据解析需要根据具体平台的接口进行实现。当前版本使用模拟数据进行演示。
