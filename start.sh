#!/bin/bash

# 社交媒体数据抓取工具启动脚本

echo "正在启动社交媒体数据抓取工具..."

# 设置环境变量以抑制Tk弃用警告
export TK_SILENCE_DEPRECATION=1

# 检查Python3是否可用
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到python3，请先安装Python 3"
    exit 1
fi

# 检查依赖包是否已安装
echo "检查依赖包..."
python3 -c "import pandas, numpy, matplotlib, openpyxl, requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
fi

# 启动程序
echo "启动程序..."
python3 main.py
