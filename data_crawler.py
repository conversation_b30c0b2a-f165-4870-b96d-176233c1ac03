import requests
import json
import time
import random
import os
from datetime import datetime
import pandas as pd
import logging

class DataCrawler:
    def __init__(self, account_manager):
        self.account_manager = account_manager
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        self.output_dir = "output_data"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 配置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler("crawler.log"),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger("DataCrawler")
    
    def crawl_account_data(self, account_id, data_types=None):
        """抓取指定账号的数据"""
        account = self.account_manager.get_account(account_id)
        if not account or not account["authorized"]:
            self.logger.error(f"账号 {account_id} 未授权或不存在")
            return False
        
        platform = account["platform"]
        cookie = account["cookie"]
        
        if not data_types:
            data_types = ["user_info", "posts", "stats"]
        
        results = {}
        
        for data_type in data_types:
            try:
                self.logger.info(f"开始抓取 {platform} 账号 {account['name']} 的 {data_type} 数据")
                if platform == "小红书":
                    data = self._crawl_xiaohongshu(account, data_type)
                elif platform == "抖音":
                    data = self._crawl_douyin(account, data_type)
                elif platform == "快手":
                    data = self._crawl_kuaishou(account, data_type)
                else:
                    self.logger.warning(f"不支持的平台: {platform}")
                    continue
                
                results[data_type] = data
                
                # 保存数据
                self._save_data(account_id, platform, data_type, data)
                
                # 添加随机延迟，避免被反爬
                time.sleep(random.uniform(2, 5))
                
            except Exception as e:
                self.logger.error(f"抓取 {platform} {data_type} 数据失败: {e}")
        
        return results
    
    def _crawl_xiaohongshu(self, account, data_type):
        """抓取小红书数据"""
        # 这里应该实现真实的API调用和数据解析
        # 以下为示例代码，模拟数据获取
        base_url = "https://www.xiaohongshu.com/api"
        
        if data_type == "user_info":
            # 模拟获取用户信息
            return {
                "account_id": account["id"],
                "name": account["name"],
                "platform": account["platform"],
                "follower_count": random.randint(1000, 100000),
                "following_count": random.randint(10, 1000),
                "post_count": random.randint(10, 500),
                "like_count": random.randint(1000, 1000000),
                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        elif data_type == "posts":
            # 模拟获取帖子列表
            posts = []
            for i in range(10):
                posts.append({
                    "post_id": f"post_{random.randint(100000, 999999)}",
                    "title": f"小红书帖子标题 {i+1}",
                    "content": f"这是小红书帖子的内容摘要 {i+1}",
                    "create_time": (datetime.now() - pd.Timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d"),
                    "likes": random.randint(10, 10000),
                    "comments": random.randint(0, 500),
                    "shares": random.randint(0, 200)
                })
            return posts
        elif data_type == "stats":
            # 模拟获取统计数据
            return {
                "account_id": account["id"],
                "engagement_rate": round(random.uniform(1.0, 10.0), 2),
                "avg_likes": random.randint(100, 5000),
                "avg_comments": random.randint(10, 200),
                "weekly_trend": {
                    "dates": [(datetime.now() - pd.Timedelta(days=i)).strftime("%Y-%m-%d") for i in range(7)],
                    "views": [random.randint(1000, 10000) for _ in range(7)],
                    "likes": [random.randint(100, 5000) for _ in range(7)],
                    "comments": [random.randint(10, 200) for _ in range(7)]
                }
            }
        
        return {"message": f"不支持的数据类型: {data_type}"}
    
    def _crawl_douyin(self, account, data_type):
        """抓取抖音数据"""
        # 这里应该实现真实的API调用和数据解析
        # 以下为示例代码，模拟数据获取
        base_url = "https://www.douyin.com/api"
        
        if data_type == "user_info":
            # 模拟获取用户信息
            return {
                "account_id": account["id"],
                "name": account["name"],
                "platform": account["platform"],
                "follower_count": random.randint(1000, 1000000),
                "following_count": random.randint(10, 1000),
                "video_count": random.randint(10, 500),
                "total_likes": random.randint(1000, ********),
                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        elif data_type == "posts":
            # 模拟获取视频列表
            posts = []
            for i in range(10):
                posts.append({
                    "video_id": f"video_{random.randint(100000, 999999)}",
                    "title": f"抖音视频标题 {i+1}",
                    "description": f"这是抖音视频的描述 {i+1}",
                    "create_time": (datetime.now() - pd.Timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d"),
                    "likes": random.randint(100, 100000),
                    "comments": random.randint(0, 5000),
                    "shares": random.randint(0, 10000)
                })
            return posts
        elif data_type == "stats":
            # 模拟获取统计数据
            return {
                "account_id": account["id"],
                "engagement_rate": round(random.uniform(1.0, 15.0), 2),
                "avg_likes": random.randint(1000, 50000),
                "avg_comments": random.randint(100, 2000),
                "weekly_trend": {
                    "dates": [(datetime.now() - pd.Timedelta(days=i)).strftime("%Y-%m-%d") for i in range(7)],
                    "views": [random.randint(10000, 1000000) for _ in range(7)],
                    "likes": [random.randint(1000, 500000) for _ in range(7)],
                    "comments": [random.randint(100, 20000) for _ in range(7)]
                }
            }
        
        return {"message": f"不支持的数据类型: {data_type}"}
    
    def _crawl_kuaishou(self, account, data_type):
        """抓取快手数据"""
        # 这里应该实现真实的API调用和数据解析
        # 以下为示例代码，模拟数据获取
        base_url = "https://www.kuaishou.com/api"
        
        if data_type == "user_info":
            # 模拟获取用户信息
            return {
                "account_id": account["id"],
                "name": account["name"],
                "platform": account["platform"],
                "follower_count": random.randint(1000, 500000),
                "following_count": random.randint(10, 1000),
                "video_count": random.randint(10, 500),
                "total_views": random.randint(10000, ********0),
                "update_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        elif data_type == "posts":
            # 模拟获取视频列表
            posts = []
            for i in range(10):
                posts.append({
                    "video_id": f"ks_video_{random.randint(100000, 999999)}",
                    "title": f"快手视频标题 {i+1}",
                    "content": f"这是快手视频的描述 {i+1}",
                    "create_time": (datetime.now() - pd.Timedelta(days=random.randint(1, 30))).strftime("%Y-%m-%d"),
                    "views": random.randint(1000, 1000000),
                    "likes": random.randint(100, 100000),
                    "comments": random.randint(0, 5000)
                })
            return posts
        elif data_type == "stats":
            # 模拟获取统计数据
            return {
                "account_id": account["id"],
                "engagement_rate": round(random.uniform(0.5, 10.0), 2),
                "avg_views": random.randint(10000, 1000000),
                "avg_likes": random.randint(1000, 50000),
                "weekly_trend": {
                    "dates": [(datetime.now() - pd.Timedelta(days=i)).strftime("%Y-%m-%d") for i in range(7)],
                    "views": [random.randint(10000, 1000000) for _ in range(7)],
                    "likes": [random.randint(1000, 500000) for _ in range(7)],
                    "comments": [random.randint(100, 20000) for _ in range(7)]
                }
            }
        
        return {"message": f"不支持的数据类型: {data_type}"}
    
    def _save_data(self, account_id, platform, data_type, data):
        """保存抓取的数据到文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{self.output_dir}/{platform}_{account_id}_{data_type}_{timestamp}.json"
        
        with open(filename, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"数据已保存到 {filename}")
    
    def export_all_to_excel(self):
        """将所有抓取的数据导出到Excel"""
        all_data = []
        
        # 遍历输出目录中的所有JSON文件
        for filename in os.listdir(self.output_dir):
            if filename.endswith(".json"):
                file_path = os.path.join(self.output_dir, filename)
                
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    
                    # 提取文件名中的信息
                    parts = filename.split("_")
                    platform = parts[0]
                    data_type = parts[2]
                    
                    # 转换数据为适合Excel的格式
                    if data_type == "user_info":
                        all_data.append({
                            "平台": platform,
                            "账号ID": data.get("account_id"),
                            "账号名称": data.get("name"),
                            "粉丝数": data.get("follower_count"),
                            "关注数": data.get("following_count"),
                            "作品数": data.get("post_count", data.get("video_count")),
                            "总点赞数": data.get("like_count", data.get("total_likes")),
                            "总播放量": data.get("total_views"),
                            "更新时间": data.get("update_time")
                        })
                    elif data_type == "stats":
                        # 提取统计数据中的关键指标
                        all_data.append({
                            "平台": platform,
                            "账号ID": data.get("account_id"),
                            "互动率(%)": data.get("engagement_rate"),
                            "平均点赞数": data.get("avg_likes"),
                            "平均评论数": data.get("avg_comments"),
                            "平均播放量": data.get("avg_views"),
                            "更新时间": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        })
                except Exception as e:
                    self.logger.error(f"处理文件 {filename} 失败: {e}")
        
        # 创建DataFrame并导出到Excel
        if all_data:
            df = pd.DataFrame(all_data)
            output_file = f"{self.output_dir}/all_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            df.to_excel(output_file, index=False)
            self.logger.info(f"所有数据已导出到 {output_file}")
            return output_file
        else:
            self.logger.warning("没有数据可导出")
            return None