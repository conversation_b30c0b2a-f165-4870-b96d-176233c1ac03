import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import threading
import os
import time
from account_manager import Account<PERSON>anager
from data_crawler import DataCrawler
from data_analyzer import DataAnalyzer

class SocialMediaDataTool:
    def __init__(self, root):
        try:
            self.root = root
            self.root.title("社交媒体数据抓取工具")
            self.root.geometry("1000x600")

            print("正在初始化账号管理器...")
            self.account_manager = AccountManager()

            print("正在初始化数据抓取器...")
            self.data_crawler = DataCrawler(self.account_manager)

            print("正在初始化数据分析器...")
            self.data_analyzer = DataAnalyzer()

            print("正在创建界面组件...")
            self.create_widgets()

            print("正在刷新账号列表...")
            self.refresh_accounts()

            print("初始化完成！")
        except Exception as e:
            print(f"初始化失败: {e}")
            import traceback
            traceback.print_exc()
            raise
    
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建顶部按钮栏
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        add_button = ttk.Button(button_frame, text="添加账号", command=self.add_account)
        add_button.pack(side=tk.LEFT, padx=5)
        
        delete_button = ttk.Button(button_frame, text="删除账号", command=self.delete_account)
        delete_button.pack(side=tk.LEFT, padx=5)
        
        crawl_button = ttk.Button(button_frame, text="抓取数据", command=self.crawl_data)
        crawl_button.pack(side=tk.LEFT, padx=5)
        
        analyze_button = ttk.Button(button_frame, text="分析数据", command=self.analyze_data)
        analyze_button.pack(side=tk.LEFT, padx=5)
        
        export_button = ttk.Button(button_frame, text="导出数据", command=self.export_data)
        export_button.pack(side=tk.LEFT, padx=5)
        
        # 创建账号列表区域
        accounts_frame = ttk.LabelFrame(main_frame, text="账号列表")
        accounts_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建账号表格
        columns = ("ID", "名称", "平台", "地址", "授权状态", "最后更新")
        self.account_tree = ttk.Treeview(accounts_frame, columns=columns, show="headings")
        
        for col in columns:
            self.account_tree.heading(col, text=col)
            if col == "ID":
                self.account_tree.column(col, width=100)
            elif col == "名称":
                self.account_tree.column(col, width=150)
            elif col == "平台":
                self.account_tree.column(col, width=100)
            elif col == "地址":
                self.account_tree.column(col, width=300)
            elif col == "授权状态":
                self.account_tree.column(col, width=100)
            else:
                self.account_tree.column(col, width=150)
        
        self.account_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加右键菜单
        self.account_menu = tk.Menu(self.root, tearoff=0)
        self.account_menu.add_command(label="登录授权", command=self.authorize_account)
        self.account_menu.add_command(label="查看详情", command=self.view_account_details)
        
        self.account_tree.bind("<Button-3>", self.show_account_menu)
        
        # 创建状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(self.root, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def refresh_accounts(self):
        """刷新账号列表"""
        # 清空现有项
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)
        
        # 添加账号
        for account in self.account_manager.accounts:
            values = (
                account["id"],
                account["name"],
                account["platform"],
                account["address"],
                "已授权" if account["authorized"] else "未授权",
                account["last_updated"] or "从未更新"
            )
            self.account_tree.insert("", tk.END, values=values)
    
    def add_account(self):
        """添加账号对话框"""
        add_window = tk.Toplevel(self.root)
        add_window.title("添加账号")
        add_window.geometry("400x250")
        add_window.resizable(False, False)
        add_window.transient(self.root)
        add_window.grab_set()
        
        # 创建表单
        frame = ttk.Frame(add_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 配置名称
        ttk.Label(frame, text="配置名称:").grid(row=0, column=0, sticky=tk.W, pady=5)
        name_entry = ttk.Entry(frame, width=30)
        name_entry.grid(row=0, column=1, sticky=tk.W, pady=5)
        
        # 平台选择
        ttk.Label(frame, text="平台:").grid(row=1, column=0, sticky=tk.W, pady=5)
        platform_var = tk.StringVar()
        platform_combobox = ttk.Combobox(frame, textvariable=platform_var, values=["小红书", "抖音", "快手"], width=27)
        platform_combobox.grid(row=1, column=1, sticky=tk.W, pady=5)
        platform_combobox.current(0)
        
        # 地址路径
        ttk.Label(frame, text="地址路径:").grid(row=2, column=0, sticky=tk.W, pady=5)
        address_entry = ttk.Entry(frame, width=30)
        address_entry.grid(row=2, column=1, sticky=tk.W, pady=5)
        
        # 提交按钮
        def on_submit():
            name = name_entry.get().strip()
            platform = platform_var.get()
            address = address_entry.get().strip()
            
            if not name or not platform or not address:
                messagebox.showerror("错误", "所有字段均为必填项")
                return
            
            # 添加账号
            self.account_manager.add_account(name, platform, address)
            self.refresh_accounts()
            add_window.destroy()
        
        submit_button = ttk.Button(frame, text="提交", command=on_submit)
        submit_button.grid(row=3, column=0, columnspan=2, pady=10)
    
    def delete_account(self):
        """删除选中的账号"""
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择要删除的账号")
            return
        
        if messagebox.askyesno("确认", "确定要删除选中的账号吗？"):
            for item in selected_items:
                account_id = self.account_tree.item(item, "values")[0]
                self.account_manager.delete_account(account_id)
            
            self.refresh_accounts()
            messagebox.showinfo("成功", "账号已删除")
    
    def show_account_menu(self, event):
        """显示账号右键菜单"""
        item = self.account_tree.identify_row(event.y)
        if item:
            self.account_tree.selection_set(item)
            self.account_menu.post(event.x_root, event.y_root)
    
    def authorize_account(self):
        """授权选中的账号"""
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择要授权的账号")
            return
        
        account_id = self.account_tree.item(selected_items[0], "values")[0]
        account = self.account_manager.get_account(account_id)
        
        if not account:
            messagebox.showerror("错误", "账号不存在")
            return
        
        if account["authorized"]:
            if not messagebox.askyesno("确认", "该账号已授权，是否重新授权？"):
                return
        
        # 更新状态
        self.status_var.set(f"正在授权 {account['name']}...")
        
        # 在后台线程中执行授权
        threading.Thread(target=self._authorize_account_thread, args=(account_id,)).start()
    
    def _authorize_account_thread(self, account_id):
        """在后台线程中执行授权"""
        try:
            def auth_callback(account_id, success):
                self.root.after(0, lambda: self._auth_callback(account_id, success))
            
            self.account_manager.authorize_account(account_id, auth_callback)
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"授权失败: {str(e)}"))
            self.status_var.set("就绪")
    
    def _auth_callback(self, account_id, success):
        """授权完成后的回调"""
        if success:
            self.refresh_accounts()
            messagebox.showinfo("成功", "账号授权成功")
        else:
            messagebox.showerror("失败", "账号授权失败")
        
        self.status_var.set("就绪")
    
    def view_account_details(self):
        """查看账号详情"""
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择要查看的账号")
            return
        
        account_id = self.account_tree.item(selected_items[0], "values")[0]
        account = self.account_manager.get_account(account_id)
        
        if not account:
            messagebox.showerror("错误", "账号不存在")
            return
        
        # 创建详情窗口
        detail_window = tk.Toplevel(self.root)
        detail_window.title(f"账号详情: {account['name']}")
        detail_window.geometry("400x300")
        detail_window.resizable(False, False)
        detail_window.transient(self.root)
        detail_window.grab_set()
        
        # 创建详情框架
        frame = ttk.Frame(detail_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # 显示账号信息
        info_text = f"配置名称: {account['name']}\n\n"
        info_text += f"平台: {account['platform']}\n\n"
        info_text += f"地址路径: {account['address']}\n\n"
        info_text += f"授权状态: {'已授权' if account['authorized'] else '未授权'}\n\n"
        info_text += f"最后更新: {account['last_updated'] or '从未更新'}\n\n"
        
        if account["authorized"] and account["cookie"]:
            info_text += f"Cookie 有效期: {time.ctime(account['cookie'].get('expires', 0))}"
        
        ttk.Label(frame, text=info_text, justify=tk.LEFT).pack(fill=tk.BOTH, expand=True)
        
        # 关闭按钮
        ttk.Button(frame, text="关闭", command=detail_window.destroy).pack(pady=10)
    
    def crawl_data(self):
        """抓取选中账号的数据"""
        selected_items = self.account_tree.selection()
        if not selected_items:
            messagebox.showinfo("提示", "请先选择要抓取数据的账号")
            return
        
        # 获取选中的账号ID
        account_ids = [self.account_tree.item(item, "values")[0] for item in selected_items]
        
        # 选择数据类型
        data_types = ["user_info", "posts", "stats"]
        data_type_window = tk.Toplevel(self.root)
        data_type_window.title("选择数据类型")
        data_type_window.geometry("300x200")
        data_type_window.resizable(False, False)
        data_type_window.transient(self.root)
        data_type_window.grab_set()
        
        # 创建选择框
        frame = ttk.Frame(data_type_window, padding="10")
        frame.pack(fill=tk.BOTH, expand=True)
        
        var_dict = {}
        for i, data_type in enumerate(data_types):
            var_dict[data_type] = tk.BooleanVar(value=True)
            ttk.Checkbutton(frame, text=data_type, variable=var_dict[data_type]).pack(anchor=tk.W, pady=2)
        
        # 抓取按钮
        def start_crawling():
            selected_types = [data_type for data_type, var in var_dict.items() if var.get()]
            
            if not selected_types:
                messagebox.showinfo("提示", "请至少选择一种数据类型")
                return
            
            data_type_window.destroy()
            
            # 更新状态
            self.status_var.set(f"正在抓取 {len(account_ids)} 个账号的数据...")
            
            # 在后台线程中执行抓取
            threading.Thread(target=self._crawl_data_thread, args=(account_ids, selected_types)).start()
        
        ttk.Button(frame, text="开始抓取", command=start_crawling).pack(pady=10)
    
    def _crawl_data_thread(self, account_ids, data_types):
        """在后台线程中执行数据抓取"""
        try:
            for i, account_id in enumerate(account_ids):
                account = self.account_manager.get_account(account_id)
                if not account or not account["authorized"]:
                    self.root.after(0, lambda id=account_id: messagebox.showinfo("提示", f"账号 {id} 未授权，跳过"))
                    continue
                
                self.root.after(0, lambda a=account["name"]: self.status_var.set(f"正在抓取 {a} 的数据..."))
                
                # 抓取数据
                results = self.data_crawler.crawl_account_data(account_id, data_types)
                
                if results:
                    # 更新账号信息
                    self.account_manager.update_account(
                        account_id,
                        last_updated=time.strftime("%Y-%m-%d %H:%M:%S")
                    )
            
            # 刷新账号列表
            self.root.after(0, self.refresh_accounts)
            
            # 导出数据到Excel
            output_file = self.data_crawler.export_all_to_excel()
            if output_file:
                self.root.after(0, lambda: messagebox.showinfo("成功", f"数据抓取完成，已导出到 {output_file}"))
            else:
                self.root.after(0, lambda: messagebox.showinfo("提示", "数据抓取完成，但没有数据可导出"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"数据抓取失败: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.status_var.set("就绪"))
    
    def analyze_data(self):
        """分析数据"""
        self.status_var.set("正在分析数据...")
        
        # 在后台线程中执行分析
        threading.Thread(target=self._analyze_data_thread).start()
    
    def _analyze_data_thread(self):
        """在后台线程中执行数据分析"""
        try:
            results = self.data_analyzer.analyze_all_data()
            
            if results:
                self.root.after(0, lambda: messagebox.showinfo("成功", "数据分析完成，结果已保存到 analysis_results 目录"))
            else:
                self.root.after(0, lambda: messagebox.showinfo("提示", "没有数据可分析"))
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"数据分析失败: {str(e)}"))
        finally:
            self.root.after(0, lambda: self.status_var.set("就绪"))
    
    def export_data(self):
        """导出账号数据"""
        output_file = self.account_manager.export_to_excel()
        messagebox.showinfo("成功", f"账号数据已导出到 {output_file}")

if __name__ == "__main__":
    try:
        print("正在启动应用程序...")
        root = tk.Tk()
        print("Tkinter根窗口创建成功")

        app = SocialMediaDataTool(root)
        print("应用程序初始化成功，开始主循环...")

        root.mainloop()
    except Exception as e:
        print(f"程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")